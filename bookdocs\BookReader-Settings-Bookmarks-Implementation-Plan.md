# BookReader Settings & Bookmarks Implementation Plan

## Overview
This document outlines the implementation plan for adding functional settings and bookmark systems to the BookReader.vue component using the Foliate JS library for ebook rendering.

## Current State Analysis

### Existing Infrastructure
- **Settings System**: Robust settings store (`settingsStore.ts`) with database persistence
- **Database Schema**: Settings table with key-value JSON storage
- **Component Structure**: BookReader.vue with placeholder buttons for settings and bookmarks
- **Book Management**: Complete book CRUD operations with metadata storage

### Foliate JS Library Capabilities
- **Supported Formats**: EPUB, MOBI, KF8 (AZW3), FB2, CBZ, PDF
- **Built-in Features**: 
  - Pagination and navigation
  - Text highlighting and annotations
  - Reading progress tracking
  - Customizable themes and typography
  - Bookmark management via CFI (Canonical Fragment Identifier)
  - Search functionality
  - Text-to-speech support

## Phase 1: Database Schema Extensions

### 1.1 New Tables for Reading Data
```sql
-- Reading settings per book
CREATE TABLE IF NOT EXISTS book_reading_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    font_size INTEGER DEFAULT 16,
    font_family TEXT DEFAULT 'Georgia',
    line_height REAL DEFAULT 1.6,
    theme TEXT DEFAULT 'light', -- 'light', 'dark', 'sepia'
    page_width INTEGER DEFAULT 800,
    margin INTEGER DEFAULT 20,
    flow TEXT DEFAULT 'paginated', -- 'paginated' or 'scrolled'
    max_column_count INTEGER DEFAULT 2,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Bookmarks using CFI for precise positioning
CREATE TABLE IF NOT EXISTS book_bookmarks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    cfi TEXT NOT NULL, -- Canonical Fragment Identifier
    title TEXT NOT NULL,
    note TEXT,
    chapter_title TEXT,
    progress_percentage REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Reading progress and position
CREATE TABLE IF NOT EXISTS book_reading_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL UNIQUE,
    current_cfi TEXT, -- Current reading position
    progress_percentage REAL DEFAULT 0.0,
    last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_reading_time INTEGER DEFAULT 0, -- in seconds
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Annotations and highlights
CREATE TABLE IF NOT EXISTS book_annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    cfi_range TEXT NOT NULL, -- CFI range for highlighted text
    selected_text TEXT NOT NULL,
    note TEXT,
    color TEXT DEFAULT '#ffeb3b', -- highlight color
    annotation_type TEXT DEFAULT 'highlight', -- 'highlight', 'note', 'underline'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

### 1.2 Books Table Extensions
```sql
-- Add book file storage fields to existing books table
ALTER TABLE books ADD COLUMN book_file_path TEXT;
ALTER TABLE books ADD COLUMN book_file_format TEXT;
ALTER TABLE books ADD COLUMN file_size INTEGER;
```

## Phase 2: Foliate JS Integration

### 2.1 Library Installation and Setup
```bash
# Install Foliate JS as a git submodule for stability
git submodule add https://github.com/johnfactotum/foliate-js.git src/lib/foliate-js
```

### 2.2 BookReader Component Restructure
```typescript
// New imports for Foliate JS
import './lib/foliate-js/view.js'

interface ReadingSettings {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  theme: 'light' | 'dark' | 'sepia';
  pageWidth: number;
  margin: number;
  flow: 'paginated' | 'scrolled';
  maxColumnCount: number;
}

interface Bookmark {
  id: number;
  cfi: string;
  title: string;
  note?: string;
  chapterTitle?: string;
  progressPercentage?: number;
  createdAt: string;
}

interface ReadingProgress {
  currentCfi?: string;
  progressPercentage: number;
  lastReadAt: string;
  totalReadingTime: number;
}
```

### 2.3 Foliate View Integration
```vue
<template>
  <div class="reading-area" ref="readingArea">
    <!-- Foliate JS custom element -->
    <foliate-view 
      ref="foliateView"
      @relocate="handleLocationChange"
      @load="handleSectionLoad"
      @create-overlayer="handleCreateOverlayer"
    ></foliate-view>
  </div>
</template>
```

## Phase 3: Settings Modal Implementation

### 3.1 ReaderSettingsModal Component
```vue
<!-- New component: src/components/modals/ReaderSettingsModal.vue -->
<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container reader-settings-modal">
      <div class="modal-header">
        <h3>Reading Settings</h3>
        <button class="close-button" @click="$emit('close')">×</button>
      </div>
      
      <div class="modal-content">
        <!-- Typography Settings -->
        <div class="settings-section">
          <h4>Typography</h4>
          <div class="setting-row">
            <label>Font Size</label>
            <input type="range" min="12" max="24" v-model="settings.fontSize" />
            <span>{{ settings.fontSize }}px</span>
          </div>
          <!-- More typography controls... -->
        </div>
        
        <!-- Theme Settings -->
        <div class="settings-section">
          <h4>Theme</h4>
          <div class="theme-options">
            <button 
              v-for="theme in themes" 
              :key="theme.value"
              :class="{ active: settings.theme === theme.value }"
              @click="settings.theme = theme.value"
            >
              {{ theme.label }}
            </button>
          </div>
        </div>
        
        <!-- Layout Settings -->
        <div class="settings-section">
          <h4>Layout</h4>
          <!-- Flow, columns, margins controls... -->
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="resetToDefaults">Reset to Defaults</button>
        <button @click="saveSettings" class="primary">Save Settings</button>
      </div>
    </div>
  </div>
</template>
```

### 3.2 Settings Persistence
```typescript
// New API handlers for reading settings
const saveReadingSettings = async (bookId: number, settings: ReadingSettings) => {
  await db.run(`
    INSERT OR REPLACE INTO book_reading_settings 
    (book_id, font_size, font_family, line_height, theme, page_width, margin, flow, max_column_count, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [bookId, settings.fontSize, settings.fontFamily, settings.lineHeight, 
      settings.theme, settings.pageWidth, settings.margin, settings.flow, 
      settings.maxColumnCount, new Date().toISOString()]);
};

const getReadingSettings = async (bookId: number): Promise<ReadingSettings> => {
  const result = await db.get(`
    SELECT * FROM book_reading_settings WHERE book_id = ?
  `, [bookId]);
  
  return result || getDefaultReadingSettings();
};
```

## Phase 4: Bookmark System Implementation

### 4.1 BookmarksPanel Component
```vue
<!-- New component: src/components/reader/BookmarksPanel.vue -->
<template>
  <div class="bookmarks-panel">
    <div class="panel-header">
      <h4>Bookmarks</h4>
      <button @click="addCurrentBookmark" class="add-bookmark-btn">
        Add Bookmark
      </button>
    </div>
    
    <div class="bookmarks-list">
      <div 
        v-for="bookmark in bookmarks" 
        :key="bookmark.id"
        class="bookmark-item"
        @click="navigateToBookmark(bookmark)"
      >
        <div class="bookmark-info">
          <div class="bookmark-title">{{ bookmark.title }}</div>
          <div class="bookmark-chapter">{{ bookmark.chapterTitle }}</div>
          <div class="bookmark-progress">{{ bookmark.progressPercentage?.toFixed(1) }}%</div>
        </div>
        <div class="bookmark-actions">
          <button @click.stop="editBookmark(bookmark)">✏️</button>
          <button @click.stop="deleteBookmark(bookmark.id)">🗑️</button>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 4.2 CFI-based Bookmark Management
```typescript
// Bookmark operations using Foliate's CFI system
const addBookmark = async (bookId: number, cfi: string, title: string, note?: string) => {
  const chapterTitle = await getCurrentChapterTitle();
  const progress = await getCurrentProgress();
  
  await db.run(`
    INSERT INTO book_bookmarks (book_id, cfi, title, note, chapter_title, progress_percentage)
    VALUES (?, ?, ?, ?, ?, ?)
  `, [bookId, cfi, title, note, chapterTitle, progress]);
};

const navigateToBookmark = async (bookmark: Bookmark) => {
  const foliateView = foliateViewRef.value;
  if (foliateView) {
    await foliateView.goTo(bookmark.cfi);
  }
};
```

## Phase 5: Integration Points

### 5.1 IPC Handlers (Electron Main Process)
```typescript
// New IPC handlers for reading functionality
ipcMain.handle('books:importFile', async (event, bookId, filePath) => {
  // Handle book file import and storage
});

ipcMain.handle('books:getReadingSettings', async (event, bookId) => {
  return await getReadingSettings(bookId);
});

ipcMain.handle('books:saveReadingSettings', async (event, bookId, settings) => {
  return await saveReadingSettings(bookId, settings);
});

ipcMain.handle('books:getBookmarks', async (event, bookId) => {
  return await getBookmarks(bookId);
});

ipcMain.handle('books:addBookmark', async (event, bookId, bookmark) => {
  return await addBookmark(bookId, bookmark);
});

ipcMain.handle('books:getReadingProgress', async (event, bookId) => {
  return await getReadingProgress(bookId);
});

ipcMain.handle('books:saveReadingProgress', async (event, bookId, progress) => {
  return await saveReadingProgress(bookId, progress);
});
```

### 5.2 Auto-save Implementation
```typescript
// Auto-save reading progress every 30 seconds
const setupAutoSave = () => {
  setInterval(async () => {
    if (currentBookId.value && foliateViewRef.value) {
      const currentLocation = await foliateViewRef.value.getCurrentLocation();
      await saveReadingProgress(currentBookId.value, {
        currentCfi: currentLocation.cfi,
        progressPercentage: currentLocation.progress,
        lastReadAt: new Date().toISOString(),
        totalReadingTime: getTotalReadingTime()
      });
    }
  }, 30000); // 30 seconds
};
```

## Phase 6: File Management

### 6.1 Book File Storage Structure
```
Books/
├── [Book ID]/
│   ├── book-file.[epub|pdf|mobi|etc] (imported book file)
│   ├── .book-meta.json (metadata)
│   ├── .cover.jpg (cover image)
│   └── notes/ (existing note folders)
```

### 6.2 File Import Handler
```typescript
const importBookFile = async (bookId: number, file: File) => {
  const bookFolder = await ensureBookFolder(bookId);
  const fileExtension = file.name.split('.').pop();
  const fileName = `book-file.${fileExtension}`;
  const filePath = path.join(bookFolder, fileName);
  
  // Save file to disk
  await fs.writeFile(filePath, Buffer.from(await file.arrayBuffer()));
  
  // Update database
  await updateBook(bookId, {
    book_file_path: filePath,
    book_file_format: fileExtension,
    file_size: file.size
  });
  
  return filePath;
};
```

## Implementation Timeline

### Week 1: Foundation
- [ ] Database schema updates
- [ ] Foliate JS integration setup
- [ ] Basic file import functionality

### Week 2: Settings System
- [ ] ReaderSettingsModal component
- [ ] Settings persistence
- [ ] Foliate view configuration

### Week 3: Bookmark System
- [ ] BookmarksPanel component
- [ ] CFI-based bookmark operations
- [ ] Navigation functionality

### Week 4: Polish & Testing
- [ ] Auto-save implementation
- [ ] Error handling
- [ ] Performance optimization
- [ ] User testing and refinement

## Technical Considerations

### Security
- Implement CSP headers to prevent script execution from ebook content
- Validate all file uploads and CFI strings
- Sanitize user input for bookmark titles and notes

### Performance
- Lazy load book content
- Implement virtual scrolling for large bookmark lists
- Cache reading settings in memory
- Debounce auto-save operations

### Accessibility
- Keyboard navigation for all reader controls
- Screen reader support for bookmarks and settings
- High contrast theme options
- Configurable font sizes for accessibility

### Error Handling
- Graceful fallbacks for unsupported book formats
- Recovery from corrupted reading progress
- User-friendly error messages
- Offline functionality preservation

## Next Steps

1. **Database Migration**: Create migration scripts for new tables
2. **Component Development**: Start with ReaderSettingsModal
3. **Foliate Integration**: Set up the library and basic rendering
4. **Testing Strategy**: Develop comprehensive test cases
5. **Documentation**: Create user guides for new features

This implementation plan provides a comprehensive roadmap for adding robust settings and bookmark functionality to the BookReader component using the powerful Foliate JS library.
